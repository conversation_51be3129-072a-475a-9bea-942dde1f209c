import { GoogleGenerativeAI } from "@google/generative-ai";
import sharp from 'sharp';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { uploadToS3 } from './storage';
import path from 'path';
import fs from 'fs';

type ImageEmbedding = number[];
type ProductImage = {
  id: string;
  url: string;
  embedding?: ImageEmbedding;
  metadata: Record<string, any>;
};

// Initialize Gemini for embeddings and analysis
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!);
const embeddingModel = genAI.getGenerativeModel({ model: 'models/embedding-001' });
const visionModel = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

// Cache for storing image embeddings
const imageEmbeddingCache = new Map<string, ImageEmbedding>();

/**
 * Generates an embedding vector for an image
 */
async function generateImageEmbedding(imageUrl: string): Promise<ImageEmbedding> {
  // Check cache first
  if (imageEmbeddingCache.has(imageUrl)) {
    return imageEmbeddingCache.get(imageUrl)!;
  }

  try {
    // @ts-ignore - Google AI API type mismatch
    const result = await embeddingModel.embedContent({
      content: {
        parts: [
          { fileData: { mimeType: 'image/jpeg', fileUri: imageUrl } },
          { text: 'Generate an embedding for visual similarity search' }
        ],
        role: 'user'
      },
      taskType: 'RETRIEVAL_DOCUMENT',
      title: 'Product Image'
    });

    const embedding = result.embedding.values;
    imageEmbeddingCache.set(imageUrl, embedding);
    return embedding;
  } catch (error) {
    console.error('Error generating image embedding:', error);
    throw new Error('Failed to generate image embedding');
  }
}

/**
 * Optimizes and compresses an image
 */
export async function optimizeImage(
  imageBuffer: Buffer, 
  options: { width?: number; quality?: number } = {}
): Promise<Buffer> {
  const { width = 1200, quality = 80 } = options;
  
  return sharp(imageBuffer)
    .resize(width, undefined, { withoutEnlargement: true })
    .webp({ quality, effort: 6 })
    .toBuffer();
}

/**
 * Detects defects in a product image
 */
export async function detectImageDefects(imageUrl: string): Promise<{
  hasDefects: boolean;
  defects: Array<{ type: string; confidence: number; description: string }>;
  suggestedReplacement?: string;
}> {
  const prompt = `Analyze this product image for any defects or quality issues. 
  Look for: poor lighting, blurriness, reflections, damage, or other visual defects.
  
  Return a JSON response with:
  - hasDefects: boolean
  - defects: array of { type: string, confidence: number, description: string }
  - suggestedReplacement: URL of a better quality image if defects are found (or null)`;

  const result = await visionModel.generateContent([
    { text: prompt },
    { fileData: { mimeType: 'image/jpeg', fileUri: imageUrl } }
  ]);

  const response = await result.response;
  const text = response.text();
  
  try {
    return JSON.parse(text);
  } catch (e) {
    console.error('Failed to parse defect detection response:', text);
    return { hasDefects: false, defects: [] };
  }
}

/**
 * Finds visually similar products
 */
export async function findSimilarProducts(
  imageUrl: string, 
  productImages: ProductImage[], 
  threshold: number = 0.85
): Promise<Array<{ id: string; url: string; similarity: number }>> {
  const targetEmbedding = await generateImageEmbedding(imageUrl);
  const similarities: Array<{ id: string; url: string; similarity: number }> = [];

  for (const product of productImages) {
    if (product.url === imageUrl) continue; // Skip self-comparison
    
    let productEmbedding = product.embedding;
    if (!productEmbedding) {
      productEmbedding = await generateImageEmbedding(product.url);
      product.embedding = productEmbedding;
    }

    const similarity = cosineSimilarity(targetEmbedding, productEmbedding);
    if (similarity >= threshold) {
      similarities.push({
        id: product.id,
        url: product.url,
        similarity
      });
    }
  }

  return similarities.sort((a, b) => b.similarity - a.similarity);
}

/**
 * Generates tags for an image using AI
 */
export async function generateImageTags(imageUrl: string): Promise<string[]> {
  const prompt = `Analyze this product image and generate relevant tags. 
  Include: product type, color, brand (if visible), material, style, and other distinguishing features.
  
  Return a JSON array of tags in lowercase, without special characters.`;

  const result = await visionModel.generateContent([
    { text: prompt },
    { fileData: { mimeType: 'image/jpeg', fileUri: imageUrl } }
  ]);

  const response = await result.response;
  const text = response.text();
  
  try {
    return JSON.parse(text);
  } catch (e) {
    console.error('Failed to parse tags:', text);
    return [];
  }
}

/**
 * Finds a better quality version of a product image online
 */
export async function findBetterQualityImage(
  imageUrl: string, 
  productName: string
): Promise<{ url: string; source: string } | null> {
  try {
    // First try to enhance the existing image
    const enhanced = await enhanceImageQuality(imageUrl);
    if (enhanced) return { url: enhanced, source: 'enhanced' };

    // If enhancement fails, try to find a better image online
    const searchQuery = `${productName} product image high quality`;
    const searchResults = await searchImagesOnline(searchQuery);
    
    if (searchResults.length > 0) {
      // Get the highest resolution image
      const bestImage = searchResults.reduce((best, current) => 
        (current.width * current.height) > (best?.width * best?.height || 0) ? current : best
      );
      
      return { 
        url: bestImage.url, 
        source: bestImage.source || 'web_search' 
      };
    }
    
    return null;
  } catch (error) {
    console.error('Error finding better quality image:', error);
    return null;
  }
}

/**
 * Enhances and brands a product image: white background, clarity, logo overlay
 */
export async function enhanceAndBrandImage(imageUrl: string, angle: string): Promise<{ url: string; angle: string; source: string }> {
  try {
    // Download image
    const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    let imageBuffer = Buffer.from(response.data);

    // Enhance (resize, white background)
    let sharpImg = sharp(imageBuffer).resize(1200, 1200, { fit: 'contain', background: { r: 255, g: 255, b: 255, alpha: 1 } });
    imageBuffer = await sharpImg.png().toBuffer();

    // Overlay logo
    const logoPath = path.join(process.cwd(), 'public', 'logo.png');
    if (fs.existsSync(logoPath)) {
      const logoBuffer = fs.readFileSync(logoPath);
      const logoResized = await sharp(logoBuffer).resize(120, 120).png().toBuffer();
      imageBuffer = await sharp(imageBuffer)
        .composite([
          { input: logoResized, top: 20, left: 1040, blend: 'over' }
        ])
        .png()
        .toBuffer();
    }

    // Upload to S3 or CDN (reuse uploadToS3 if available)
    const fileName = `rivv-bulk-${uuidv4()}-${angle}.png`;
    const url = await uploadToS3(imageBuffer, fileName, 'image/png');
    return { url, angle, source: 'enhanced_original' };
  } catch (error) {
    console.error('Error enhancing/branding image:', error);
    return { url: imageUrl, angle, source: 'original' };
  }
}

/**
 * For a given product, fetch and group images by angle, enhance, and brand them
 */
export async function processProductImages({
  productName,
  brand,
  colorway,
  originalImageUrl
}: {
  productName: string;
  brand: string;
  colorway?: string;
  originalImageUrl: string;
}): Promise<{
  images: Array<{ angle: string; url: string }>;
  images_source: 'search_api' | 'enhanced_original';
}> {
  // Define desired angles
  const angles = ['front', 'side_left', 'side_right', 'back', 'top', 'sole'];
  let foundImages: Array<{ angle: string; url: string }> = [];
  let usedSource: 'search_api' | 'enhanced_original' = 'search_api';

  // Try to find better images for each angle
  for (const angle of angles) {
    const query = `${brand} ${productName} ${colorway || ''} ${angle} view sneaker white background`;
    const searchResults = await searchImagesOnline(query);
    // Prefer white background, high-res
    const best = searchResults.find(img => img.url && img.width > 400 && img.height > 400);
    if (best) {
      // Enhance and brand
      const branded = await enhanceAndBrandImage(best.url, angle);
      foundImages.push({ angle, url: branded.url });
    }
  }

  // If no images found, fallback to original
  if (foundImages.length === 0) {
    const branded = await enhanceAndBrandImage(originalImageUrl, 'main');
    foundImages = [{ angle: 'main', url: branded.url }];
    usedSource = 'enhanced_original';
  }

  return {
    images: foundImages,
    images_source: usedSource
  };
}

// Helper function to calculate cosine similarity between two vectors
function cosineSimilarity(vecA: number[], vecB: number[]): number {
  if (vecA.length !== vecB.length) return 0;
  
  const dotProduct = vecA.reduce((sum, val, i) => sum + val * vecB[i], 0);
  const magnitudeA = Math.sqrt(vecA.reduce((sum, val) => sum + val * val, 0));
  const magnitudeB = Math.sqrt(vecB.reduce((sum, val) => sum + val * val, 0));
  
  if (magnitudeA === 0 || magnitudeB === 0) return 0;
  return dotProduct / (magnitudeA * magnitudeB);
}

// Helper function to enhance image quality using Sharp
async function enhanceImageQuality(imageUrl: string): Promise<string | null> {
  try {
    const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    let imageBuffer = Buffer.from(response.data);

    const maxSize = parseInt(process.env.IMAGE_MAX_SIZE || '1200');
    const quality = parseInt(process.env.IMAGE_ENHANCEMENT_QUALITY || '90');

    // Enhance image: resize, sharpen, optimize
    const enhancedBuffer = await sharp(imageBuffer)
      .resize(maxSize, maxSize, {
        fit: 'inside',
        withoutEnlargement: true,
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      })
      .sharpen(1, 1, 0.5) // Mild sharpening
      .jpeg({ quality, progressive: true })
      .toBuffer();

    // Upload enhanced image
    const fileName = `enhanced_${uuidv4()}.jpg`;
    const enhancedUrl = await uploadToS3(enhancedBuffer, fileName, 'image/jpeg');

    return enhancedUrl;
  } catch (error) {
    console.error('Error enhancing image quality:', error);
    return null;
  }
}

// Helper function to search for images online using Google Custom Search API
async function searchImagesOnline(query: string): Promise<Array<{url: string; width: number; height: number; source?: string}>> {
  try {
    const apiKey = process.env.GOOGLE_CUSTOM_SEARCH_API_KEY;
    const searchEngineId = process.env.GOOGLE_CUSTOM_SEARCH_ENGINE_ID;
    const limit = parseInt(process.env.SEARCH_RESULTS_LIMIT || '20');

    if (!apiKey || !searchEngineId) {
      console.warn('Google Custom Search API not configured');
      return [];
    }

    const searchUrl = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=${encodeURIComponent(query)}&searchType=image&num=${Math.min(limit, 10)}&imgSize=large&imgType=photo&safe=active`;

    const response = await axios.get(searchUrl);

    if (!response.data.items) {
      return [];
    }

    return response.data.items.map((item: any) => ({
      url: item.link,
      width: parseInt(item.image?.width || '0'),
      height: parseInt(item.image?.height || '0'),
      source: item.displayLink || 'google_search'
    })).filter((img: any) => img.width > 300 && img.height > 300); // Filter for decent quality

  } catch (error) {
    console.error('Error searching for images:', error);
    return [];
  }
}

export {
  generateImageEmbedding
};

export type {
  ProductImage,
  ImageEmbedding
};
